{"name": "@jupyterlab/galata-extension", "version": "5.4.6", "private": true, "description": "JupyterLab UI Testing Framework Extension.", "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "jupyterlab-extension"], "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "main": "../lib/extension/index.js", "types": "../lib/extension/index.d.ts", "files": ["../lib/extension/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}"], "scripts": {"build": "npm run build:lib && npm run build:labextension", "build:labextension": "node ../../node_modules/@jupyterlab/builder/lib/build-labextension.js --core-path ../../dev_mode --development .", "build:lib": "tsc", "clean": "npm run clean:lib && npm run clean:labextension", "clean:labextension": "rimraf ../../jupyterlab/galata/@jupyterlab", "clean:lib": "rimraf ../lib/extension tsconfig.tsbuildinfo"}, "dependencies": {"@jupyterlab/application": "^4.4.6", "@jupyterlab/apputils": "^4.5.6", "@jupyterlab/cells": "^4.4.6", "@jupyterlab/debugger": "^4.4.6", "@jupyterlab/docmanager": "^4.4.6", "@jupyterlab/nbformat": "^4.4.6", "@jupyterlab/notebook": "^4.4.6", "@jupyterlab/settingregistry": "^4.4.6", "@lumino/algorithm": "^2.0.3", "@lumino/coreutils": "^2.2.1", "@lumino/signaling": "^2.1.4"}, "devDependencies": {"@jupyterlab/builder": "^4.4.6", "rimraf": "~5.0.5", "typescript": "~5.5.4"}, "jupyterlab": {"extension": true, "outputDir": "../../jupyterlab/galata/@jupyterlab/galata-extension", "_build": {"load": "static/remoteEntry.185b344b4ead4fc619dd.js", "extension": "./extension"}}}